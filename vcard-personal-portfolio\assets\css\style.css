/*-----------------------------------*\
  #style.css
\*-----------------------------------*/


/**
 * copyright 2022 @codewithsadee
 */





/*-----------------------------------*\
  #CUSTOM PROPERTY
\*-----------------------------------*/

:root {

  /**
   * colors
   */

  /* gradient */

  --bg-gradient-onyx: linear-gradient(
    to bottom right, 
    hsl(240, 1%, 25%) 3%, 
    hsl(0, 0%, 19%) 97%
  );
  --bg-gradient-jet: linear-gradient(
    to bottom right, 
    hsla(240, 1%, 18%, 0.251) 0%, 
    hsla(240, 2%, 11%, 0) 100%
  ), hsl(240, 2%, 13%);
  --bg-gradient-yellow-1: linear-gradient(
    to bottom right, 
    hsl(45, 100%, 71%) 0%, 
    hsla(36, 100%, 69%, 0) 50%
  );
  --bg-gradient-yellow-2: linear-gradient(
    135deg, 
    hsla(45, 100%, 71%, 0.251) 0%, 
    hsla(35, 100%, 68%, 0) 59.86%
  ), hsl(240, 2%, 13%);
  --border-gradient-onyx: linear-gradient(
    to bottom right, 
    hsl(0, 0%, 25%) 0%, 
    hsla(0, 0%, 25%, 0) 50%
  );
  --text-gradient-yellow: linear-gradient(
    to right, 
    hsl(45, 100%, 72%), 
    hsl(35, 100%, 68%)
  );

  /* solid */

  --jet: hsl(0, 0%, 22%);
  --onyx: hsl(240, 1%, 17%);
  --eerie-black-1: hsl(240, 2%, 13%);
  --eerie-black-2: hsl(240, 2%, 12%);
  --smoky-black: hsl(0, 0%, 7%);
  --white-1: hsl(0, 0%, 100%);
  --white-2: hsl(0, 0%, 98%);
  --orange-yellow-crayola: hsl(45, 100%, 72%);
  --vegas-gold: hsl(45, 54%, 58%);
  --light-gray: hsl(0, 0%, 84%);
  --light-gray-70: hsla(0, 0%, 84%, 0.7);
  --bittersweet-shimmer: hsl(0, 43%, 51%);

  /**
   * typography
   */

  /* font-family */
  --ff-poppins: 'Poppins', sans-serif;

  /* font-size */
  --fs-1: 24px;
  --fs-2: 18px;
  --fs-3: 17px;
  --fs-4: 16px;
  --fs-5: 15px;
  --fs-6: 14px;
  --fs-7: 13px;
  --fs-8: 11px;

  /* font-weight */
  --fw-300: 300;
  --fw-400: 400;
  --fw-500: 500;
  --fw-600: 600;

  /**
   * shadow
   */
  
  --shadow-1: -4px 8px 24px hsla(0, 0%, 0%, 0.25);
  --shadow-2: 0 16px 30px hsla(0, 0%, 0%, 0.25);
  --shadow-3: 0 16px 40px hsla(0, 0%, 0%, 0.25);
  --shadow-4: 0 25px 50px hsla(0, 0%, 0%, 0.15);
  --shadow-5: 0 24px 80px hsla(0, 0%, 0%, 0.25);

  /**
   * transition
   */

  --transition-1: 0.25s ease;
  --transition-2: 0.5s ease-in-out;

}





/*-----------------------------------*\
  #RESET
\*-----------------------------------*/

*, *::before, *::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

a { text-decoration: none; }

li { list-style: none; }

img, ion-icon, a, button, time, span { display: block; }

button {
  font: inherit;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
}

input, textarea {
  display: block;
  width: 100%;
  background: none;
  font: inherit;
}

::selection {
  background: var(--orange-yellow-crayola);
  color: var(--smoky-black);
}

:focus { outline-color: var(--orange-yellow-crayola); }

html { font-family: var(--ff-poppins); }

body { background: var(--smoky-black); }





/*-----------------------------------*\
  #REUSED STYLE
\*-----------------------------------*/

.sidebar,
article {
  background: var(--eerie-black-2);
  border: 1px solid var(--jet);
  border-radius: 20px;
  padding: 15px;
  box-shadow: var(--shadow-1);
  z-index: 1;
}

.separator {
  width: 100%;
  height: 1px;
  background: var(--jet);
  margin: 16px 0;
}

.icon-box {
  position: relative;
  background: var(--border-gradient-onyx);
  width: 30px;
  height: 30px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: var(--orange-yellow-crayola);
  box-shadow: var(--shadow-1);
  z-index: 1;
}

.icon-box::before {
  content: "";
  position: absolute;
  inset: 1px;
  background: var(--eerie-black-1);
  border-radius: inherit;
  z-index: -1;
}

.icon-box ion-icon { --ionicon-stroke-width: 35px; }

article { display: none; }

article.active {
  display: block;
  animation: fade 0.5s ease backwards;
}

@keyframes fade {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

.h2,
.h3,
.h4,
.h5 {
  color: var(--white-2);
  text-transform: capitalize;
}

.h2 { font-size: var(--fs-1); }

.h3 { font-size: var(--fs-2); }

.h4 { font-size: var(--fs-4); }

.h5 {
  font-size: var(--fs-7);
  font-weight: var(--fw-500);
}

.article-title {
  position: relative;
  padding-bottom: 7px;
}

.article-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 30px;
  height: 3px;
  background: var(--text-gradient-yellow);
  border-radius: 3px;
}

.has-scrollbar::-webkit-scrollbar {
  width: 5px; /* for vertical scrollbar */
  height: 5px; /* for horizontal scrollbar */
}

.has-scrollbar::-webkit-scrollbar-track {
  background: var(--onyx);
  border-radius: 5px;
}

.has-scrollbar::-webkit-scrollbar-thumb {
  background: var(--orange-yellow-crayola);
  border-radius: 5px;
}

.has-scrollbar::-webkit-scrollbar-button { width: 20px; }

.content-card {
  position: relative;
  background: var(--border-gradient-onyx);
  padding: 15px;
  padding-top: 45px;
  border-radius: 14px;
  box-shadow: var(--shadow-2);
  cursor: pointer;
  z-index: 1;
}

.content-card::before {
  content: "";
  position: absolute;
  inset: 1px;
  background: var(--bg-gradient-jet);
  border-radius: inherit;
  z-index: -1;
}





/*-----------------------------------*\
  #MAIN
\*-----------------------------------*/

main {
  margin: 15px 12px;
  margin-bottom: 75px;
  min-width: 259px;
}





/*-----------------------------------*\
  #SIDEBAR
\*-----------------------------------*/

.sidebar {
  margin-bottom: 15px;
  max-height: 112px;
  overflow: hidden;
  transition: var(--transition-2);
}

.sidebar.active { max-height: 405px; }

.sidebar-info {
  position: relative;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 15px;
}

.avatar-box {
  position: relative;
  background: var(--border-gradient-onyx);
  border-radius: 20px;
  padding: 4px;
  box-shadow: var(--shadow-2);
  transition: var(--transition-1);
  z-index: 1;
}

.avatar-box::before {
  content: "";
  position: absolute;
  inset: 2px;
  background: var(--bg-gradient-onyx);
  border-radius: inherit;
  z-index: -1;
}

.avatar-box::after {
  content: "";
  position: absolute;
  inset: -2px;
  background: var(--bg-gradient-yellow-1);
  border-radius: 24px;
  opacity: 0;
  transition: var(--transition-1);
  z-index: -2;
}

.avatar-box:hover::after {
  opacity: 0.3;
}

.avatar-box img {
  border-radius: 16px;
  transition: var(--transition-1);
  filter: brightness(1.05) contrast(1.1);
}

.avatar-box:hover img {
  transform: scale(1.02);
  filter: brightness(1.1) contrast(1.15);
}

.info-content .name {
  color: var(--white-2);
  font-size: var(--fs-3);
  font-weight: var(--fw-500);
  letter-spacing: -0.25px;
  margin-bottom: 10px;
}

.info-content .title {
  color: var(--white-1);
  background: var(--onyx);
  font-size: var(--fs-8);
  font-weight: var(--fw-300);
  width: max-content;
  padding: 3px 12px;
  border-radius: 8px;
}

.info_more-btn {
  position: absolute;
  top: -15px;
  right: -15px;
  border-radius: 0 15px;
  font-size: 13px;
  color: var(--orange-yellow-crayola);
  background: var(--border-gradient-onyx);
  padding: 10px;
  box-shadow: var(--shadow-2);
  transition: var(--transition-1);
  z-index: 1;
}

.info_more-btn::before {
  content: "";
  position: absolute;
  inset: 1px;
  border-radius: inherit;
  background: var(--bg-gradient-jet);
  transition: var(--transition-1);
  z-index: -1;
}

.info_more-btn:hover,
.info_more-btn:focus { background: var(--bg-gradient-yellow-1); }

.info_more-btn:hover::before,
.info_more-btn:focus::before { background: var(--bg-gradient-yellow-2); }

.info_more-btn span { display: none; }

.sidebar-info_more {
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-2);
}

.sidebar.active .sidebar-info_more {
  opacity: 1;
  visibility: visible;
}

.contacts-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.contact-item {
  min-width: 100%;
  display: flex;
  align-items: center;
  gap: 16px;
}

.contact-info {
  max-width: calc(100% - 46px);
  width: calc(100% - 46px);
}

.contact-title {
  color: var(--light-gray-70);
  font-size: var(--fs-8);
  text-transform: uppercase;
  margin-bottom: 2px;
}

.contact-info :is(.contact-link, time, address) {
  color: var(--white-2);
  font-size: var(--fs-7);
}

.contact-info address { font-style: normal; }

.social-list {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 15px;
  padding-bottom: 4px;
  padding-left: 7px;
}

.social-item .social-link {
  color: var(--light-gray-70);
  font-size: 18px;
}


.social-item .social-link:hover { color: var(--light-gray); }





/*-----------------------------------*\
  #NAVBAR
\*-----------------------------------*/

.navbar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: hsla(240, 1%, 17%, 0.75);
  backdrop-filter: blur(10px);
  border: 1px solid var(--jet);
  border-radius: 12px 12px 0 0;
  box-shadow: var(--shadow-2);
  z-index: 5;
}

.navbar-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  padding: 0 10px;
}

.navbar-link {
  color: var(--light-gray);
  font-size: var(--fs-8);
  padding: 20px 7px;
  transition: color var(--transition-1);
}

.navbar-link:hover,
.navbar-link:focus { color: var(--light-gray-70); }

.navbar-link.active { color: var(--orange-yellow-crayola); }





/*-----------------------------------*\
  #ABOUT
\*-----------------------------------*/

.about .article-title { margin-bottom: 15px; }

.about-text {
  color: var(--light-gray);
  font-size: var(--fs-6);
  font-weight: var(--fw-300);
  line-height: 1.6;
}

.about-text p { margin-bottom: 15px; }



/**
 * #service 
 */

.service { margin-bottom: 35px; }

.service-title { margin-bottom: 20px; }

.service-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.service-item {
  position: relative;
  background: var(--border-gradient-onyx);
  padding: 20px;
  border-radius: 14px;
  box-shadow: var(--shadow-2);
  z-index: 1;
}

.service-item::before {
  content: "";
  position: absolute;
  inset: 1px;
  background: var(--bg-gradient-jet);
  border-radius: inherit;
  z-index: -1;
}

.service-icon-box { margin-bottom: 10px; }

.service-icon-box img { margin: auto; }

.service-content-box { text-align: center; }

.service-item-title { margin-bottom: 7px; }

.service-item-text {
  color: var(--light-gray);
  font-size: var(--fs-6);
  font-weight: var(--fw-3);
  line-height: 1.6;
}




.modal-close-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  background: var(--onyx);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--white-2);
  font-size: 18px;
  opacity: 0.7;
}

.modal-close-btn:hover,
.modal-close-btn:focus { opacity: 1; }

.modal-close-btn ion-icon { --ionicon-stroke-width: 50px; }

.modal-avatar-box {
  background: var(--bg-gradient-onyx);
  width: max-content;
  border-radius: 14px;
  margin-bottom: 15px;
  box-shadow: var(--shadow-2);
}

.modal-img-wrapper > img { display: none; }

.modal-title { margin-bottom: 4px; }

.modal-content time {
  font-size: var(--fs-6);
  color: var(--light-gray-70);
  font-weight: var(--fw-300);
  margin-bottom: 10px;
}

.modal-content p {
  color: var(--light-gray);
  font-size: var(--fs-6);
  font-weight: var(--fw-300);
  line-height: 1.6;
}


/**
 * #clients 
 */

.clients { margin-bottom: 15px; }

.clients-list {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 15px;
  margin: 0 -15px;
  padding: 25px;
  padding-bottom: 25px;
  overflow-x: auto;
  scroll-behavior: smooth;
  overscroll-behavior-inline: contain;
  scroll-snap-type: inline mandatory;
  scroll-padding-inline: 25px;
}

.clients-item {
  min-width: 50%;
  scroll-snap-align: start;
}

.clients-item img {
  width: 100%;
  filter: grayscale(1);
  transition: var(--transition-1);
}

.clients-item img:hover { filter: grayscale(0); }





/*-----------------------------------*\
  #RESUME
\*-----------------------------------*/

.article-title { margin-bottom: 30px; }

/**
 * Resume Download Button
 */

.resume-download-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  margin-top: 25px;
  padding: 0 20px;
}

.resume-download-btn {
  position: relative;
  background: linear-gradient(135deg, hsl(45, 100%, 72%) 0%, hsl(35, 100%, 68%) 100%);
  color: hsl(0, 0%, 12%);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 15px 25px;
  border: 2px solid hsl(45, 100%, 72%);
  border-radius: 14px;
  font-size: var(--fs-6);
  font-weight: var(--fw-600);
  text-transform: capitalize;
  box-shadow: 0 8px 24px hsla(45, 100%, 72%, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1;
  min-width: 180px;
}

.resume-download-btn::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, hsl(45, 100%, 75%) 0%, hsl(35, 100%, 70%) 100%);
  border-radius: inherit;
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.resume-download-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 32px hsla(45, 100%, 72%, 0.4);
  border-color: hsl(45, 100%, 75%);
}

.resume-download-btn:hover::before {
  opacity: 1;
}

.resume-download-btn ion-icon {
  font-size: 20px;
  --ionicon-stroke-width: 60px;
}

.resume-download-btn span {
  font-weight: var(--fw-600);
  letter-spacing: 0.5px;
}

.resume-download-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px hsla(45, 100%, 72%, 0.25);
}

.resume-download-btn:focus {
  outline: 2px solid hsl(45, 100%, 72%);
  outline-offset: 2px;
}


/**
 * education and experience 
 */

.timeline { margin-bottom: 30px; }

.timeline .title-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 25px;
}

.timeline-list {
  font-size: var(--fs-6);
  margin-left: 45px;
}

.timeline-item { position: relative; }

.timeline-item:not(:last-child) { margin-bottom: 20px; }

.timeline-item-title {
  font-size: var(--fs-6);
  line-height: 1.3;
  margin-bottom: 7px;
}

.timeline-list span {
  color: var(--vegas-gold);
  font-weight: var(--fw-400);
  line-height: 1.6;
}

.timeline-item:not(:last-child)::before {
  content: "";
  position: absolute;
  top: -25px;
  left: -30px;
  width: 1px;
  height: calc(100% + 50px);
  background: var(--jet);
}

.timeline-item::after {
  content: "";
  position: absolute;
  top: 5px;
  left: -33px;
  height: 6px;
  width: 6px;
  background: var(--text-gradient-yellow);
  border-radius: 50%;
  box-shadow: 0 0 0 4px var(--jet);
}

.timeline-text {
  color: var(--light-gray);
  font-weight: var(--fw-300);
  line-height: 1.6;
}


/**
 * skills 
 */

.skills-title { margin-bottom: 20px; }

.skills-list { padding: 20px; }


.skills-item:not(:last-child) { margin-bottom: 15px; }

.skill .title-wrapper {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 8px;
}

.skill .title-wrapper data {
  color: var(--light-gray);
  font-size: var(--fs-7);
  font-weight: var(--fw-300);
}

.skill-progress-bg {
  background: var(--jet);
  width: 100%;
  height: 8px;
  border-radius: 10px;
}

.skill-progress-fill {
  background: var(--text-gradient-yellow);
  height: 100%;
  border-radius: inherit;
}





/*-----------------------------------*\
  #PROJECTS
\*-----------------------------------*/

.projects-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
  margin-bottom: 10px;
}

.project-card {
  background: var(--border-gradient-onyx);
  border: 1px solid var(--jet);
  border-radius: 20px;
  padding: 0;
  overflow: hidden;
  transition: var(--transition-1);
  position: relative;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-4);
}

.project-image {
  position: relative;
  width: 100%;
  height: 200px;
  background: linear-gradient(135deg, var(--smoky-black) 0%, var(--eerie-black) 100%);
  overflow: hidden;
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-network {
  width: 80%;
  height: 80%;
  background-image:
    radial-gradient(circle at 20% 30%, var(--orange-yellow-crayola) 2px, transparent 2px),
    radial-gradient(circle at 80% 20%, var(--orange-yellow-crayola) 2px, transparent 2px),
    radial-gradient(circle at 60% 70%, var(--orange-yellow-crayola) 2px, transparent 2px),
    radial-gradient(circle at 30% 80%, var(--orange-yellow-crayola) 2px, transparent 2px),
    radial-gradient(circle at 90% 60%, var(--orange-yellow-crayola) 2px, transparent 2px);
  background-size: 100% 100%;
  position: relative;
}

.project-network::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(45deg, transparent 48%, var(--orange-yellow-crayola) 49%, var(--orange-yellow-crayola) 51%, transparent 52%),
    linear-gradient(-45deg, transparent 48%, var(--orange-yellow-crayola) 49%, var(--orange-yellow-crayola) 51%, transparent 52%);
  background-size: 40px 40px, 40px 40px;
  background-position: 0 0, 20px 20px;
  opacity: 0.3;
  animation: networkPulse 3s ease-in-out infinite;
}

@keyframes networkPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

.project-content {
  padding: 25px;
}

.project-title {
  color: var(--white-2);
  font-size: var(--fs-4);
  font-weight: var(--fw-600);
  margin-bottom: 15px;
  line-height: 1.2;
}

.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.tag {
  background: var(--bg-gradient-yellow-1);
  color: var(--smoky-black);
  font-size: var(--fs-8);
  font-weight: var(--fw-500);
  padding: 4px 12px;
  border-radius: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.project-description {
  color: var(--light-gray-70);
  font-size: var(--fs-6);
  line-height: 1.6;
  margin-bottom: 20px;
}

.project-links {
  display: flex;
  gap: 15px;
}

.project-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--orange-yellow-crayola);
  font-size: var(--fs-7);
  font-weight: var(--fw-500);
  text-decoration: none;
  transition: var(--transition-1);
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid transparent;
}

.project-link:hover {
  color: var(--white-2);
  background: var(--bg-gradient-yellow-1);
  border-color: var(--orange-yellow-crayola);
  transform: translateY(-2px);
}

.project-link ion-icon {
  font-size: 16px;
  --ionicon-stroke-width: 50px;
}





/*-----------------------------------*\
  #BLOG
\*-----------------------------------*/

.blog-posts { margin-bottom: 10px; }

.blog-posts-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.blog-post-item > a {
  position: relative;
  background: var(--border-gradient-onyx);
  height: 100%;
  box-shadow: var(--shadow-4);
  border-radius: 16px;
  z-index: 1;
}

.blog-post-item > a::before {
  content: "";
  position: absolute;
  inset: 1px;
  border-radius: inherit;
  background: var(--eerie-black-1);
  z-index: -1;
}

.blog-banner-box {
  width: 100%;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
}

.blog-banner-box img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-1);
}

.blog-post-item > a:hover .blog-banner-box img { transform: scale(1.1); }

.blog-content { padding: 15px; }

.blog-meta {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 7px;
  margin-bottom: 10px;
}

.blog-meta :is(.blog-category, time) {
  color: var(--light-gray-70);
  font-size: var(--fs-6);
  font-weight: var(--fw-300);
}

.blog-meta .dot {
  background: var(--light-gray-70);
  width: 4px;
  height: 4px;
  border-radius: 4px;
}

.blog-item-title {
  margin-bottom: 10px;
  line-height: 1.3;
  transition: var(--transition-1);
}

.blog-post-item > a:hover .blog-item-title { color: var(--orange-yellow-crayola); }

.blog-text {
  color: var(--light-gray);
  font-size: var(--fs-6);
  font-weight: var(--fw-300);
  line-height: 1.6;
}





/*-----------------------------------*\
  #CONTACT
\*-----------------------------------*/

.mapbox {
  position: relative;
  height: 250px;
  width: 100%;
  border-radius: 16px;
  margin-bottom: 30px;
  border: 1px solid var(--jet);
  overflow: hidden;
}

.mapbox figure { height: 100%; }

.mapbox iframe {
  width: 100%;
  height: 100%;
  border: none;
  filter: grayscale(1) invert(1);
}

.contact-form { margin-bottom: 10px; }

.form-title { margin-bottom: 20px; }

.input-wrapper {
  display: grid;
  grid-template-columns: 1fr;
  gap: 25px;
  margin-bottom: 25px;
}

.form-input {
  color: var(--white-2);
  font-size: var(--fs-6);
  font-weight: var(--fw-400);
  padding: 13px 20px;
  border: 1px solid var(--jet);
  border-radius: 14px;
  outline: none;
}

.form-input::placeholder { font-weight: var(--fw-500); }

.form-input:focus { border-color: var(--orange-yellow-crayola); }

textarea.form-input {
  min-height: 100px;
  height: 120px;
  max-height: 200px;
  resize: vertical;
  margin-bottom: 25px;
}

textarea.form-input::-webkit-resizer { display: none; }

.form-input:focus:invalid { border-color: var(--bittersweet-shimmer); }

.form-btn {
  position: relative;
  width: 100%;
  background: var(--border-gradient-onyx);
  color: var(--orange-yellow-crayola);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 13px 20px;
  border-radius: 14px;
  font-size: var(--fs-6);
  text-transform: capitalize;
  box-shadow: var(--shadow-3);
  z-index: 1;
  transition: var(--transition-1);
}

.form-btn::before {
  content: "";
  position: absolute;
  inset: 1px;
  background: var(--bg-gradient-jet);
  border-radius: inherit;
  z-index: -1;
  transition: var(--transition-1);
}

.form-btn ion-icon { font-size: 16px; }

.form-btn:hover { background: var(--bg-gradient-yellow-1); }

.form-btn:hover::before { background: var(--bg-gradient-yellow-2); }

.form-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.form-btn:disabled:hover { background: var(--border-gradient-onyx); }

.form-btn:disabled:hover::before { background: var(--bg-gradient-jet); }





/*-----------------------------------*\
  #RESPONSIVE
\*-----------------------------------*/

/**
 * responsive larger than 450px screen
 */

@media (min-width: 450px) {

  /**
   * client
   */

  .clients-item { min-width: calc(33.33% - 10px); }



  /**
   * #PROJECTS, BLOG
   */

  .project-image,
  .blog-banner-box { height: auto; }

}





/**
 * responsive larger than 580px screen
 */

@media (min-width: 580px) {

  /**
   * CUSTOM PROPERTY
   */

  :root {

    /**
     * typography
     */

    --fs-1: 32px;
    --fs-2: 24px;
    --fs-3: 26px;
    --fs-4: 18px;
    --fs-6: 15px;
    --fs-7: 15px;
    --fs-8: 12px;

  }



  /**
   * #REUSED STYLE
   */

  .sidebar, article {
    width: 520px;
    margin-inline: auto;
    padding: 30px;
  }

  .article-title {
    font-weight: var(--fw-600);
    padding-bottom: 15px;
  }

  .article-title::after {
    width: 40px;
    height: 5px;
  }

  .icon-box {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    font-size: 18px;
  }



  /**
   * #MAIN
   */

  main {
    margin-top: 60px;
    margin-bottom: 100px;
  }



  /**
   * #SIDEBAR
   */

  .sidebar {
    max-height: 180px;
    margin-bottom: 30px;
  }

  .sidebar.active { max-height: 584px; }

  .sidebar-info { gap: 25px; }

  .avatar-box {
    border-radius: 30px;
    padding: 5px;
    box-shadow: var(--shadow-3);
  }

  .avatar-box::before {
    inset: 3px;
    border-radius: 27px;
  }

  .avatar-box::after {
    inset: -3px;
    border-radius: 36px;
  }

  .avatar-box img {
    border-radius: 25px;
  }

  .avatar-box img { width: 120px; }

  .info-content .name { margin-bottom: 15px; }

  .info-content .title { padding: 5px 18px; }

  .info_more-btn {
    top: -30px;
    right: -30px;
    padding: 10px 15px;
  }

  .info_more-btn span {
    display: block;
    font-size: var(--fs-8);
  }

  .info_more-btn ion-icon { display: none; }

  .separator { margin: 32px 0; }

  .contacts-list { gap: 20px; }

  .contact-info {
    max-width: calc(100% - 64px);
    width: calc(100% - 64px);
  }



  /**
   * #NAVBAR
   */

  .navbar { border-radius: 20px 20px 0 0; }

  .navbar-list { gap: 20px; }

  .navbar-link { --fs-8: 14px; }



  /**
   * #ABOUT
   */

  .about .article-title { margin-bottom: 20px; }

  .about-text { margin-bottom: 40px; }

  /* service */

  .service-item {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 18px;
    padding: 30px;
  }

  .service-icon-box {
    margin-bottom: 0;
    margin-top: 5px;
  }

  .service-content-box { text-align: left; }



  /* clients */

  .clients-list {
    gap: 50px;
    margin: 0 -30px;
    padding: 45px;
    scroll-padding-inline: 45px;
  }

  .clients-item { min-width: calc(33.33% - 35px); }



  /**
   * #RESUME
   */

  .timeline-list { margin-left: 65px; }

  .timeline-item:not(:last-child)::before { left: -40px; }

  .timeline-item::after {
    height: 8px;
    width: 8px;
    left: -43px;
  }

  .skills-item:not(:last-child) { margin-bottom: 25px; }



  /**
   * #PROJECTS, BLOG
   */

  .project-image, .blog-banner-box { border-radius: 16px; }

  .blog-posts-list { gap: 30px; }

  .blog-content { padding: 25px; }



  /**
   * #CONTACT
   */

  .mapbox {
    height: 380px;
    border-radius: 18px;
  }

  .input-wrapper {
    gap: 30px;
    margin-bottom: 30px;
  }

  .form-input { padding: 15px 20px; }

  textarea.form-input { margin-bottom: 30px; }

  .form-btn {
    --fs-6: 16px;
    padding: 16px 20px;
  }

  .form-btn ion-icon { font-size: 18px; }

}





/**
 * responsive larger than 768px screen
 */

@media (min-width: 768px) {

  /**
   * REUSED STYLE
   */

  .sidebar, article { width: 700px; }

  .has-scrollbar::-webkit-scrollbar-button { width: 100px; }



  /**
   * SIDEBAR
   */

  .contacts-list {
    grid-template-columns: 1fr 1fr;
    gap: 30px 15px;
  }



  /**
   * NAVBAR
   */

  .navbar-link { --fs-8: 15px; }



  /**
   * ABOUT
   */





  /**
   * PROJECTS
   */

  .article-title { padding-bottom: 20px; }

  /* projects and blog grid */

  .projects-grid, .blog-posts-list { grid-template-columns: 1fr 1fr; }



  /**
   * CONTACT
   */

  .input-wrapper { grid-template-columns: 1fr 1fr; }

  .form-btn {
    width: max-content;
    margin-left: auto;
  }
  
}





/**
 * responsive larger than 1024px screen
 */

@media (min-width: 1024px) {

  /**
   * CUSTOM PROPERTY
   */

  :root {

    /**
    * shadow
    */

    --shadow-1: -4px 8px 24px hsla(0, 0%, 0%, 0.125);
    --shadow-2: 0 16px 30px hsla(0, 0%, 0%, 0.125);
    --shadow-3: 0 16px 40px hsla(0, 0%, 0%, 0.125);

  }



  /**
   * REUSED STYLE
   */

  .sidebar, article {
    width: 950px;
    box-shadow: var(--shadow-5);
  }



  /**
   * MAIN 
   */

  main { margin-bottom: 60px; }

  .main-content {
    position: relative;
    width: max-content;
    margin: auto;
  }



  /**
   * NAVBAR
   */

  .navbar {
    position: absolute;
    bottom: auto;
    top: 0;
    left: auto;
    right: 0;
    width: max-content;
    border-radius: 0 20px;
    padding: 0 20px;
    box-shadow: none;
  }

  .navbar-list {
    gap: 30px;
    padding: 0 20px;
  }

  .navbar-link { font-weight: var(--fw-500); }



  /**
   * ABOUT
   */

  /* service */

  .service-list {
    grid-template-columns: 1fr 1fr;
    gap: 20px 25px;
  }



  /* clients */

  .clients-item { min-width: calc(25% - 38px); }



  /**
   * PROJECTS
   */

  .projects-grid { grid-template-columns: repeat(3, 1fr); }



  /**
   * BLOG
   */

  .blog-banner-box { height: 230px; }

}





/**
 * responsive larger than 1250px screen
 */

@media (min-width: 1250px) {

  /**
   * RESET
   */

  body::-webkit-scrollbar { width: 20px; }

  body::-webkit-scrollbar-track { background: var(--smoky-black); }

  body::-webkit-scrollbar-thumb {
    border: 5px solid var(--smoky-black);
    background: hsla(0, 0%, 100%, 0.1);
    border-radius: 20px;
    box-shadow: inset 1px 1px 0 hsla(0, 0%, 100%, 0.11),
                inset -1px -1px 0 hsla(0, 0%, 100%, 0.11);
  }

  body::-webkit-scrollbar-thumb:hover { background: hsla(0, 0%, 100%, 0.15); }

  body::-webkit-scrollbar-button { height: 60px; }



  /**
   * REUSED STYLE
   */

  .sidebar, article { width: auto; }

  article { min-height: 100%; }



  /**
   * MAIN
   */

  main {
    max-width: 1200px;
    margin-inline: auto;
    display: flex;
    justify-content: center;
    align-items: stretch;
    gap: 25px;
  }

  .main-content {
    min-width: 75%;
    width: 75%;
    margin: 0;
  }



  /**
   * SIDEBAR
   */

  .sidebar {
    position: sticky;
    top: 60px;
    max-height: max-content;
    height: 100%;
    margin-bottom: 0;
    padding-top: 60px;
    z-index: 1;
  }

  .sidebar-info { flex-direction: column; }

  .avatar-box {
    padding: 6px;
    box-shadow: var(--shadow-5);
  }

  .avatar-box::before {
    inset: 4px;
  }

  .avatar-box::after {
    inset: -4px;
  }

  .avatar-box img {
    width: 150px;
    border-radius: 20px;
  }

  .info-content .name {
    white-space: nowrap;
    text-align: center;
  }

  .info-content .title { margin: auto; }

  .info_more-btn { display: none; }

  .sidebar-info_more {
    opacity: 1;
    visibility: visible;
  }

  .contacts-list { grid-template-columns: 1fr; }

  .contact-info :is(.contact-link) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .contact-info :is(.contact-link, time, address) {
    --fs-7: 14px;
    font-weight: var(--fw-300);
  }

  .separator:last-of-type {
    margin: 15px 0;
    opacity: 0;
  }

  .social-list { justify-content: center; }



  /**
	 * RESUME
	 */

  .timeline-text { max-width: 700px; }

  /* Resume download button responsive */
  .resume-download-container {
    justify-content: center;
  }

  .resume-download-btn {
    padding: 18px 30px;
    font-size: var(--fs-5);
  }

  .resume-download-btn ion-icon {
    font-size: 20px;
  }

}