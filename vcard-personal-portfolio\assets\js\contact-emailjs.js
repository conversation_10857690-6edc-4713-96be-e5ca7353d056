// Alternative Contact Form Implementation using EmailJS
// Replace the contact form section in script.js with this code if you prefer EmailJS over Formspree

// Initialize EmailJS (you'll need to replace with your actual keys)
emailjs.init("YOUR_PUBLIC_KEY"); // Replace with your EmailJS public key

// contact form variables
const form = document.querySelector("[data-form]");
const formInputs = document.querySelectorAll("[data-form-input]");
const formBtn = document.querySelector("[data-form-btn]");
const successMessage = document.getElementById("successMessage");
const errorMessage = document.getElementById("errorMessage");

// add event to all form input field
for (let i = 0; i < formInputs.length; i++) {
  formInputs[i].addEventListener("input", function () {

    // check form validation
    if (form.checkValidity()) {
      formBtn.removeAttribute("disabled");
    } else {
      formBtn.setAttribute("disabled", "");
    }

  });
}

// handle form submission with EmailJS
form.addEventListener("submit", function(e) {
  e.preventDefault();
  
  // Hide any previous messages
  successMessage.style.display = "none";
  errorMessage.style.display = "none";
  
  // Show loading state
  const originalText = formBtn.querySelector('span').textContent;
  const originalIcon = formBtn.querySelector('ion-icon').name;
  formBtn.querySelector('span').textContent = 'Sending...';
  formBtn.querySelector('ion-icon').name = 'hourglass-outline';
  formBtn.setAttribute('disabled', '');
  
  // Get form data
  const templateParams = {
    from_name: form.fullname.value,
    from_email: form.email.value,
    message: form.message.value,
    to_name: 'Mohamed Shamil', // Your name
  };
  
  // Send email using EmailJS
  emailjs.send('YOUR_SERVICE_ID', 'YOUR_TEMPLATE_ID', templateParams)
    .then(function(response) {
      // Success
      console.log('SUCCESS!', response.status, response.text);
      successMessage.style.display = "flex";
      form.reset();
      
      // Reset button state
      formBtn.querySelector('span').textContent = 'Message Sent!';
      formBtn.querySelector('ion-icon').name = 'checkmark-outline';
      
      // Reset to original state after 3 seconds
      setTimeout(() => {
        formBtn.querySelector('span').textContent = originalText;
        formBtn.querySelector('ion-icon').name = originalIcon;
        formBtn.setAttribute('disabled', '');
        successMessage.style.display = "none";
      }, 3000);
      
    }, function(error) {
      // Error
      console.log('FAILED...', error);
      errorMessage.style.display = "flex";
      
      // Reset button state
      formBtn.querySelector('span').textContent = originalText;
      formBtn.querySelector('ion-icon').name = originalIcon;
      
      // Re-enable button if form is valid
      if (form.checkValidity()) {
        formBtn.removeAttribute("disabled");
      }
      
      // Hide error message after 5 seconds
      setTimeout(() => {
        errorMessage.style.display = "none";
      }, 5000);
    });
});
